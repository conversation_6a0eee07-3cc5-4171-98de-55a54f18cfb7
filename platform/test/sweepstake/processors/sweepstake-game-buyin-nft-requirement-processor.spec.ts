import { describe, it } from '@tcom/test';
import { anything, instance, mock, reset, verify, when } from '@tcom/test/mock';
import { SweepstakeGameBuyInNFTRequirementProcessor } from '../../../src/sweepstake/processors';
import { NFTOwnershipChecker } from '../../../src/nft';
import { BlockchainNetwork } from '../../../src/blockchain';
import { expect } from 'chai';

describe('SweepstakeGameBuyInNFTRequirementProcessor', () => {
    const mockNFTOwnershipChecker = mock(NFTOwnershipChecker);

    function getProcessor(): SweepstakeGameBuyInNFTRequirementProcessor {
        return new SweepstakeGameBuyInNFTRequirementProcessor(
            instance(mockNFTOwnershipChecker)
        );
    }

    beforeEach(() => {
        reset(mockNFTOwnershipChecker);
    });

    describe('process()', () => {
        it('should return true when no NFT requirements exist', async () => {
            // Given
            const sweepstake = {
                id: 1,
                metadata: {}
            } as any;

            const userId = 123;
            const processor = getProcessor();

            // When
            const result = await processor.process(sweepstake, userId);

            // Then
            expect(result.passed).to.be.true;
            verify(mockNFTOwnershipChecker.checkAnyNFTOwnership(anything(), anything())).never();
        });

        it('should return true when NFT requirements array is empty', async () => {
            // Given
            const sweepstake = {
                id: 1,
                metadata: {
                    nftHolderRequirements: []
                }
            } as any;

            const userId = 123;
            const processor = getProcessor();

            // When
            const result = await processor.process(sweepstake, userId);

            // Then
            expect(result.passed).to.be.true;
            verify(mockNFTOwnershipChecker.checkAnyNFTOwnership(anything(), anything())).never();
        });

        it('should return true when user owns at least one NFT from required collections', async () => {
            // Given
            const nftRequirements = [
                {
                    contractAddress: '0x123456789abcdef',
                    network: BlockchainNetwork.Ethereum
                },
                {
                    contractAddress: '0xabcdef123456789',
                    network: BlockchainNetwork.Polygon
                }
            ];

            const sweepstake = {
                id: 1,
                metadata: {
                    nftHolderRequirements: nftRequirements
                }
            } as any;

            const userId = 123;
            const processor = getProcessor();

            when(mockNFTOwnershipChecker.checkAnyNFTOwnership(userId, anything())).thenResolve(true);

            // When
            const result = await processor.process(sweepstake, userId);

            // Then
            expect(result.passed).to.be.true;
            verify(mockNFTOwnershipChecker.checkAnyNFTOwnership(userId, anything())).once();
        });

        it('should return false when user does not own any NFTs from required collections', async () => {
            // Given
            const nftRequirements = [
                {
                    contractAddress: '0x123456789abcdef',
                    network: BlockchainNetwork.Ethereum
                },
                {
                    contractAddress: '0xabcdef123456789',
                    network: BlockchainNetwork.Polygon
                }
            ];

            const sweepstake = {
                id: 1,
                metadata: {
                    nftHolderRequirements: nftRequirements
                }
            } as any;

            const userId = 123;
            const processor = getProcessor();

            when(mockNFTOwnershipChecker.checkAnyNFTOwnership(userId, anything())).thenResolve(false);

            // When
            const result = await processor.process(sweepstake, userId);

            // Then
            expect(result.passed).to.be.false;
            verify(mockNFTOwnershipChecker.checkAnyNFTOwnership(userId, anything())).once();
        });
    });
});
